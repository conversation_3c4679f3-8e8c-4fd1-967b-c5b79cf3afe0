<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scribling - Handwriting Recognition AI</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 900px;
            padding: 40px;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        h1 {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
            font-weight: 400;
        }

        .upload-section {
            margin-bottom: 30px;
        }

        .drop-zone {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px 20px;
            text-align: center;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .drop-zone:hover {
            border-color: #764ba2;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            transform: translateY(-2px);
        }

        .drop-zone.dragover {
            border-color: #28a745;
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
            transform: scale(1.02);
        }

        .drop-zone-content {
            position: relative;
            z-index: 2;
        }

        .upload-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 15px;
            display: block;
        }

        .drop-text {
            font-size: 1.2rem;
            color: #555;
            margin-bottom: 10px;
            font-weight: 500;
        }

        .drop-subtext {
            color: #888;
            font-size: 0.9rem;
        }

        #file-input {
            display: none;
        }

        .image-preview-container {
            margin: 30px 0;
            text-align: center;
            display: none;
        }

        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .image-item {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }

        .image-item:hover {
            transform: scale(1.02);
        }

        .image-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            display: block;
        }

        .image-item .remove-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 16px;
            color: #ff4757;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .image-item .remove-btn:hover {
            background: #ff4757;
            color: white;
            transform: scale(1.1);
        }

        .images-count {
            text-align: center;
            margin-bottom: 15px;
            color: #667eea;
            font-weight: 600;
        }

        #image-preview {
            max-width: 100%;
            max-height: 400px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }

        #image-preview:hover {
            transform: scale(1.02);
        }

        .action-section {
            text-align: center;
            margin: 30px 0;
        }

        .read-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .read-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .read-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .processing-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .processing-content {
            background: white;
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            max-width: 400px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .spinner {
            width: 60px;
            height: 60px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .processing-text {
            font-size: 1.2rem;
            color: #333;
            font-weight: 600;
        }

        .processing-subtext {
            color: #666;
            margin-top: 10px;
        }

        .result-section {
            margin-top: 40px;
        }

        .result-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }

        .result-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-right: 15px;
        }

        .result-badge {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        #result-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            min-height: 150px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        #result-container.empty {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-style: italic;
        }

        .error {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-top: 15px;
            font-weight: 500;
        }

        .powered-by {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #888;
            font-size: 0.9rem;
        }

        .powered-by::before {
            content: "⚡";
            margin-right: 5px;
        }

        /* Markdown Styles */
        #result-container h1, #result-container h2, #result-container h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 10px;
        }

        #result-container h1 { font-size: 1.5rem; }
        #result-container h2 { font-size: 1.3rem; }
        #result-container h3 { font-size: 1.1rem; }

        #result-container p {
            margin-bottom: 15px;
        }

        #result-container ul, #result-container ol {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        #result-container code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: "Courier New", monospace;
        }

        #result-container pre {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
        }

        #result-container blockquote {
            border-left: 4px solid #667eea;
            padding-left: 15px;
            margin: 15px 0;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="processing-overlay" id="processing-overlay">
        <div class="processing-content">
            <div class="spinner"></div>
            <div class="processing-text">AI is processing...</div>
            <div class="processing-subtext">This may take a few moments</div>
        </div>
    </div>

    <div class="container">
        <div class="header">
            <h1>Scribling - Handwriting Recognition AI</h1>
            <p class="subtitle">Upload an image of handwritten text, and our AI will transcribe it for you.</p>
        </div>

        <div class="upload-section">
            <div class="drop-zone" id="drop-zone">
                <div class="drop-zone-content">
                    <span class="upload-icon">📁</span>
                    <div class="drop-text">Drop your images here or click to browse</div>
                    <div class="drop-subtext">Supports JPG, PNG, GIF, and other image formats • Multiple images supported</div>
                </div>
            </div>
            <input type="file" id="file-input" accept="image/*" multiple>
        </div>

        <div class="image-preview-container" id="image-preview-container">
            <div class="images-count" id="images-count"></div>
            <div class="images-grid" id="images-grid"></div>
        </div>

        <div class="action-section">
            <button class="read-button" id="read-button" disabled>
                🔍 Read Text from Images
            </button>
        </div>

        <div class="result-section" id="result-section" style="display: none;">
            <div class="result-header">
                <div class="result-title">Transcribed Text</div>
                <div class="result-badge">AI Output</div>
            </div>
            <div id="result-container" class="empty">
                Your transcribed text will appear here...
            </div>
        </div>

        <div class="powered-by">
            Powered by Dakoii AI - Testing Environment
        </div>
    </div>

    <script>
        const dropZone = document.getElementById('drop-zone');
        const fileInput = document.getElementById('file-input');
        const imagePreviewContainer = document.getElementById('image-preview-container');
        const imagesGrid = document.getElementById('images-grid');
        const imagesCount = document.getElementById('images-count');
        const readButton = document.getElementById('read-button');
        const processingOverlay = document.getElementById('processing-overlay');
        const resultContainer = document.getElementById('result-container');
        const resultSection = document.getElementById('result-section');

        let uploadedFiles = [];
        let imageDataArray = [];

        // Initialize drag and drop functionality
        function initializeDragAndDrop() {
            // Prevent default drag behaviors
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });

            // Highlight drop zone when item is dragged over it
            ['dragenter', 'dragover'].forEach(eventName => {
                dropZone.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, unhighlight, false);
            });

            // Handle dropped files
            dropZone.addEventListener('drop', handleDrop, false);

            // Handle click to browse
            dropZone.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', handleFileSelect);
        }

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlight() {
            dropZone.classList.add('dragover');
        }

        function unhighlight() {
            dropZone.classList.remove('dragover');
        }

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }

        function handleFileSelect(e) {
            const files = e.target.files;
            handleFiles(files);
        }

        function handleFiles(files) {
            const validFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
            
            if (validFiles.length === 0) {
                showError('Please select valid image files.');
                return;
            }

            validFiles.forEach(file => {
                if (!uploadedFiles.some(f => f.name === file.name && f.size === file.size)) {
                    uploadedFiles.push(file);
                    displayImage(file);
                }
            });
            
            updateUI();
        }

        function displayImage(file) {
            const reader = new FileReader();

            reader.onload = (e) => {
                const imageData = e.target.result.split(',')[1];
                imageDataArray.push({
                    data: imageData,
                    mimeType: file.type,
                    fileName: file.name
                });

                // Create image preview element
                const imageItem = document.createElement('div');
                imageItem.className = 'image-item';
                imageItem.innerHTML = `
                    <img src="${e.target.result}" alt="${file.name}">
                    <button class="remove-btn" onclick="removeImage(${uploadedFiles.length - 1})">×</button>
                `;

                imagesGrid.appendChild(imageItem);
                updateButtonState();
            };

            reader.readAsDataURL(file);
        }

        // Global function for removing images
        window.removeImage = function(index) {
            uploadedFiles.splice(index, 1);
            imageDataArray.splice(index, 1);
            
            // Clear and rebuild the grid
            imagesGrid.innerHTML = '';
            imageDataArray.length = 0;
            
            // Re-process remaining files
            uploadedFiles.forEach(file => {
                displayImage(file);
            });
            
            updateUI();
        };

        function removeImage(index) {
            window.removeImage(index);
        }

        function updateUI() {
            if (uploadedFiles.length > 0) {
                imagePreviewContainer.style.display = 'block';
                imagesCount.textContent = `${uploadedFiles.length} image${uploadedFiles.length > 1 ? 's' : ''} selected`;
                
                // Update drop zone to show success
                dropZone.innerHTML = `
                    <div class="drop-zone-content">
                        <span class="upload-icon">✅</span>
                        <div class="drop-text">${uploadedFiles.length} image${uploadedFiles.length > 1 ? 's' : ''} uploaded successfully!</div>
                        <div class="drop-subtext">Click here to add more images or select different ones</div>
                    </div>
                `;
            } else {
                imagePreviewContainer.style.display = 'none';
                
                // Reset drop zone
                dropZone.innerHTML = `
                    <div class="drop-zone-content">
                        <span class="upload-icon">📁</span>
                        <div class="drop-text">Drop your images here or click to browse</div>
                        <div class="drop-subtext">Supports JPG, PNG, GIF, and other image formats • Multiple images supported</div>
                    </div>
                `;
            }
        }

        function updateButtonState() {
            readButton.disabled = imageDataArray.length === 0;
        }

        function showProcessing() {
            const processingText = document.querySelector('.processing-text');
            processingText.textContent = `AI is reading your handwriting from ${imageDataArray.length} image${imageDataArray.length > 1 ? 's' : ''}...`;
            processingOverlay.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function hideProcessing() {
            processingOverlay.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function showError(message) {
            resultSection.style.display = 'block';
            resultContainer.innerHTML = `<div class="error">❌ ${message}</div>`;
            resultContainer.classList.remove('empty');
        }

        function showResult(text) {
            resultSection.style.display = 'block';
            resultContainer.classList.remove('empty');

            // Parse and render markdown
            try {
                const htmlContent = marked.parse(text);
                resultContainer.innerHTML = htmlContent;
            } catch (error) {
                // Fallback to plain text if markdown parsing fails
                resultContainer.textContent = text;
            }
        }

        // Event listeners
        readButton.addEventListener('click', async () => {
            if (imageDataArray.length === 0) {
                showError("Please upload at least one image first.");
                return;
            }

            showProcessing();

            try {
                const response = await fetch('api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        images: imageDataArray
                    }),
                });

                const data = await response.json();

                if (data.success) {
                    showResult(data.text);
                } else {
                    showError(data.error);
                }

            } catch (error) {
                console.error("Error:", error);
                showError(`Network error: ${error.message}`);
            } finally {
                hideProcessing();
                updateButtonState();
            }
        });

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            initializeDragAndDrop();
        });
    </script>
</body>
</html>
