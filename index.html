<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hand Writing Text Recognition AI</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: #f4f7f9;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
            text-align: center;
        }
        h1 {
            color: #1a253c;
            margin-bottom: 20px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        .step {
            margin-bottom: 25px;
            text-align: left;
        }
        .step-label {
            font-weight: bold;
            display: block;
            margin-bottom: 8px;
            color: #555;
        }
        input[type="file"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 6px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }
        button:disabled {
            background-color: #a0cfff;
            cursor: not-allowed;
        }
        button:hover:not(:disabled) {
            background-color: #0056b3;
        }
        #image-preview {
            max-width: 100%;
            max-height: 300px;
            margin-top: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
            display: none;
        }
        #result-container {
            margin-top: 30px;
            text-align: left;
            background-color: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 20px;
            white-space: pre-wrap;
            font-family: "Courier New", Courier, monospace;
            line-height: 1.6;
            min-height: 100px;
        }
        #loader {
            display: none;
            margin-top: 20px;
            font-weight: bold;
            color: #007bff;
        }
        .powered-by {
            margin-top: 20px;
            font-size: 12px;
            color: #888;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Hand Writing Text Recognition AI</h1>
        <p class="subtitle">Upload an image of handwritten text, and our AI will transcribe it for you using Gemini 2.5 Pro.</p>

        <div class="step">
            <label for="image-upload" class="step-label">Step 1: Upload an Image of Your Handwritten Text</label>
            <input type="file" id="image-upload" accept="image/*">
        </div>

        <img id="image-preview" src="#" alt="Your uploaded image">

        <div class="step">
            <label class="step-label">Step 2: Read the Text</label>
            <button id="read-button" disabled>Read Text from Image</button>
        </div>

        <div id="loader">Reading your handwritten text... please wait.</div>

        <div id="result-container">
            Your transcribed text will appear here...
        </div>

        <div class="powered-by">
            Powered by Google Gemini 2.5 Pro
        </div>
    </div>

    <script>
        const imageUpload = document.getElementById('image-upload');
        const imagePreview = document.getElementById('image-preview');
        const readButton = document.getElementById('read-button');
        const loader = document.getElementById('loader');
        const resultContainer = document.getElementById('result-container');

        let imageData = null;

        function updateButtonState() {
            readButton.disabled = !imageData;
        }

        imageUpload.addEventListener('change', (event) => {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                
                reader.onload = (e) => {
                    imagePreview.src = e.target.result;
                    imagePreview.style.display = 'block';
                    imageData = e.target.result.split(',')[1];
                    updateButtonState();
                };
                
                reader.readAsDataURL(file);
            }
        });

        readButton.addEventListener('click', async () => {
            if (!imageData) {
                alert("Please upload an image first.");
                return;
            }

            loader.style.display = 'block';
            resultContainer.textContent = '';
            readButton.disabled = true;

            try {
                const response = await fetch('api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image: imageData,
                        mimeType: 'image/jpeg'
                    }),
                });

                const data = await response.json();

                if (data.success) {
                    resultContainer.textContent = data.text;
                } else {
                    resultContainer.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                }

            } catch (error) {
                console.error("Error:", error);
                resultContainer.innerHTML = `<div class="error">An error occurred: ${error.message}</div>`;
            } finally {
                loader.style.display = 'none';
                updateButtonState();
            }
        });
    </script>
</body>
</html>
