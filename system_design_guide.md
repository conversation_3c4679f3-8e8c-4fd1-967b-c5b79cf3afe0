Of course! Here is a complete, self-contained HTML file with JavaScript that accomplishes your goal.

You can copy and paste this code into a new file, save it as notes_reader.html, and open it in your web browser.

Key Features of this Code:

No Server Needed: This runs entirely in the user's browser (client-side).

Secure API Key Handling: The API key is entered by the user and is not saved or stored anywhere. It's only used for the API call during the session.

Image Preview: The user sees the image they've uploaded.

Loading State: A simple "Reading..." message appears while waiting for the AI's response.

Clear Instructions: The UI guides the user through the steps.

Error Handling: It displays a user-friendly message if the API call fails.

Uses Gemini 1.5 Pro: The code is specifically configured to call the gemini-1.5-pro-latest model with your specified parameters.

notes_reader.html
code
Html
download
content_copy
expand_less

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Handwritten Notes Reader AI</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: #f4f7f9;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
            text-align: center;
        }
        h1 {
            color: #1a253c;
            margin-bottom: 20px;
        }
        .step {
            margin-bottom: 25px;
            text-align: left;
        }
        .step-label {
            font-weight: bold;
            display: block;
            margin-bottom: 8px;
            color: #555;
        }
        input[type="file"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 6px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }
        button:disabled {
            background-color: #a0cfff;
            cursor: not-allowed;
        }
        button:hover:not(:disabled) {
            background-color: #0056b3;
        }
        #image-preview {
            max-width: 100%;
            max-height: 300px;
            margin-top: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
            display: none; /* Hidden by default */
        }
        #result-container {
            margin-top: 30px;
            text-align: left;
            background-color: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 20px;
            white-space: pre-wrap; /* Preserves line breaks and spaces */
            font-family: "Courier New", Courier, monospace;
            line-height: 1.6;
            min-height: 100px;
        }
        #loader {
            display: none;
            margin-top: 20px;
            font-weight: bold;
            color: #007bff;
        }
        .api-note {
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>Handwritten Notes Reader AI</h1>
        <p>Upload an image of handwritten notes, and Gemini 1.5 Pro will transcribe the text for you.</p>

        <div class="step">
            <label for="api-key" class="step-label">Step 1: Enter your Google AI Studio API Key</label>
            <input type="password" id="api-key" placeholder="Enter your API Key here">
            <p class="api-note">Your key is not stored. It's used only for this session. Get a key from <a href="https://aistudio.google.com/app/apikey" target="_blank">Google AI Studio</a>.</p>
        </div>

        <div class="step">
            <label for="image-upload" class="step-label">Step 2: Upload an Image of Your Notes</label>
            <input type="file" id="image-upload" accept="image/*">
        </div>

        <img id="image-preview" src="#" alt="Your uploaded image">

        <div class="step">
            <label class="step-label">Step 3: Read the Text</label>
            <button id="read-button" disabled>Read Text from Image</button>
        </div>

        <div id="loader">Reading your notes... please wait.</div>

        <div id="result-container">
            Your transcribed text will appear here...
        </div>
    </div>

    <script>
        // Get references to the DOM elements
        const apiKeyInput = document.getElementById('api-key');
        const imageUpload = document.getElementById('image-upload');
        const imagePreview = document.getElementById('image-preview');
        const readButton = document.getElementById('read-button');
        const loader = document.getElementById('loader');
        const resultContainer = document.getElementById('result-container');

        // Store the image data in a variable
        let imageData = null;

        // Function to enable/disable the button
        function updateButtonState() {
            if (apiKeyInput.value.trim() !== '' && imageData) {
                readButton.disabled = false;
            } else {
                readButton.disabled = true;
            }
        }

        // Listen for changes in the API key input
        apiKeyInput.addEventListener('input', updateButtonState);

        // Listen for when a user selects an image
        imageUpload.addEventListener('change', (event) => {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                
                reader.onload = (e) => {
                    // Show the image preview
                    imagePreview.src = e.target.result;
                    imagePreview.style.display = 'block';
                    
                    // Store the base64 encoded image data (without the data: prefix)
                    imageData = e.target.result.split(',')[1];
                    updateButtonState();
                };
                
                // Read the file as a Data URL (which is base64 encoded)
                reader.readAsDataURL(file);
            }
        });

        // Listen for the button click to start the AI process
        readButton.addEventListener('click', async () => {
            if (!imageData || !apiKeyInput.value) {
                alert("Please provide an API key and upload an image.");
                return;
            }

            // Show loader and clear previous results
            loader.style.display = 'block';
            resultContainer.textContent = '';
            readButton.disabled = true;

            const apiKey = apiKeyInput.value.trim();
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-latest:generateContent?key=${apiKey}`;

            const requestBody = {
                "contents": [{
                    "parts": [
                        { "text": "Read the handwritten text from this image and write it out. Transcribe it exactly as it appears, preserving line breaks and structure." },
                        {
                            "inline_data": {
                                "mime_type": "image/jpeg", // Assuming JPEG, but Gemini is flexible
                                "data": imageData
                            }
                        }
                    ]
                }],
                "generationConfig": {
                    "temperature": 0,
                    "maxOutputTokens": 8192, // Max for Gemini 1.5 Pro is 8192
                }
            };

            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody),
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`API Error: ${response.status} - ${errorData.error.message}`);
                }

                const data = await response.json();
                
                // Extract the text from the response
                const text = data.candidates[0]?.content?.parts[0]?.text;

                if (text) {
                    resultContainer.textContent = text;
                } else {
                    resultContainer.textContent = "Could not extract text from the image. The response might be empty or blocked.";
                }

            } catch (error) {
                console.error("Error:", error);
                resultContainer.textContent = `An error occurred: ${error.message}`;
            } finally {
                // Hide loader and re-enable the button
                loader.style.display = 'none';
                updateButtonState();
            }
        });
    </script>

</body>
</html>
How to Use It:

Get a Gemini API Key:

Go to Google AI Studio.

Sign in with your Google account.

Click "Create API key" and copy the key.

Save the Code:

Open a plain text editor (like Notepad on Windows, or VS Code).

Copy the entire HTML code block above.

Paste it into the editor.

Save the file with an .html extension, for example, notes_reader.html.

Run the Application:

Find the notes_reader.html file you just saved.

Double-click it to open it in your default web browser (like Chrome, Firefox, or Edge).

Follow the On-Screen Steps:

Step 1: Paste your Gemini API key into the first input box.

Step 2: Click "Choose File" and select an image of handwritten notes from your computer. You will see a preview of the image.

Step 3: The "Read Text from Image" button will become clickable. Click it.

The "Reading your notes..." message will appear. After a few seconds, the transcribed text will be displayed in the box at the bottom.



GEMINI DOCS

Gemini API docs

Gemini Developer API
Get a Gemini API Key
Get a Gemini API key and make your first API request in minutes.

Python
JavaScript
Go
Java
REST

curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent" \
  -H "x-goog-api-key: $GEMINI_API_KEY" \
  -H 'Content-Type: application/json' \
  -X POST \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "Explain how AI works in a few words"
          }
        ]
      }
    ]
  }'
Meet the models

Model variants
The Gemini API offers different models that are optimized for specific use cases. Here's a brief overview of Gemini variants that are available:

Model variant	Input(s)	Output	Optimized for
Gemini 2.5 Pro
gemini-2.5-pro	Audio, images, videos, text, and PDF	Text	Enhanced thinking and reasoning, multimodal understanding, advanced coding, and more

API KEY = AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA