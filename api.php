<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Load environment variables
function loadEnv($path) {
    if (!file_exists($path)) {
        return false;
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        
        if (!array_key_exists($name, $_SERVER) && !array_key_exists($name, $_ENV)) {
            putenv(sprintf('%s=%s', $name, $value));
            $_ENV[$name] = $value;
            $_SERVER[$name] = $value;
        }
    }
    return true;
}

// Load .env file
if (!loadEnv('.env')) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Configuration file not found']);
    exit;
}

// Get configuration from environment
$geminiApiKey = getenv('GEMINI_API_KEY');
$geminiModel = getenv('GEMINI_MODEL') ?: 'gemini-2.5-pro';
$geminiApiUrl = getenv('GEMINI_API_URL') ?: 'https://generativelanguage.googleapis.com/v1beta/models/';

$openrouterApiKey = getenv('OPENROUTER_API_KEY');
$openrouterModel = getenv('OPENROUTER_MODEL') ?: 'qwen/qwen3-vl-235b-a22b-instruct';
$openrouterApiUrl = getenv('OPENROUTER_API_URL') ?: 'https://openrouter.ai/api/v1/chat/completions';

$maxTokens = getenv('MAX_OUTPUT_TOKENS') ?: 64192;
$temperature = getenv('TEMPERATURE') ?: 0;

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['image'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'No image data provided']);
    exit;
}

$imageData = $input['image'];
$mimeType = $input['mimeType'] ?? 'image/jpeg';
$selectedModel = $input['model'] ?? 'gemini';

// Validate API keys based on selected model
if ($selectedModel === 'gemini' && !$geminiApiKey) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Gemini API key not configured']);
    exit;
}

if ($selectedModel === 'openrouter' && !$openrouterApiKey) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'OpenRouter API key not configured']);
    exit;
}

// Prepare API request based on selected model
if ($selectedModel === 'gemini') {
    $requestUrl = $geminiApiUrl . $geminiModel . ':generateContent?key=' . $geminiApiKey;

    $requestBody = [
        'contents' => [
            [
                'parts' => [
                    [
                        //'text' => 'Read the handwritten text from this image and transcribe it exactly as it appears, preserving line breaks and structure. If there are any drawings or non-text elements, describe them briefly.'
                        'text' => 'Read this texts and write them out.'
                    ],
                    [
                        'inline_data' => [
                            'mime_type' => $mimeType,
                            'data' => $imageData
                        ]
                    ]
                ]
            ]
        ],
        'generationConfig' => [
            'temperature' => (float)$temperature,
            'maxOutputTokens' => (int)$maxTokens
        ]
    ];

    $headers = ['Content-Type: application/json'];

} elseif ($selectedModel === 'openrouter') {
    $requestUrl = $openrouterApiUrl;

    $requestBody = [
        'model' => $openrouterModel,
        'messages' => [
            [
                'role' => 'user',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => 'Read this texts and write them out.'
                    ],
                    [
                        'type' => 'image_url',
                        'image_url' => [
                            'url' => 'data:' . $mimeType . ';base64,' . $imageData
                        ]
                    ]
                ]
            ]
        ],
        'max_tokens' => (int)$maxTokens,
        'temperature' => (float)$temperature
    ];

    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $openrouterApiKey
    ];
} else {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid model selected']);
    exit;
}

// Make the API call
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $requestUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestBody));
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

if ($curlError) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Network error: ' . $curlError]);
    exit;
}

if ($httpCode !== 200) {
    http_response_code($httpCode);
    $errorData = json_decode($response, true);
    $errorMessage = $errorData['error']['message'] ?? 'Unknown API error';
    echo json_encode(['success' => false, 'error' => 'API Error: ' . $errorMessage]);
    exit;
}

// Parse the response
$responseData = json_decode($response, true);

if (!$responseData) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Invalid response from AI service']);
    exit;
}

$transcribedText = '';

// Parse response based on model
if ($selectedModel === 'gemini') {
    if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
        $transcribedText = $responseData['candidates'][0]['content']['parts'][0]['text'];
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Invalid Gemini response format']);
        exit;
    }
} elseif ($selectedModel === 'openrouter') {
    if (isset($responseData['choices'][0]['message']['content'])) {
        $transcribedText = $responseData['choices'][0]['message']['content'];
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Invalid OpenRouter response format']);
        exit;
    }
}

if (empty($transcribedText)) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'No text extracted from response']);
    exit;
}

// Return success response
echo json_encode([
    'success' => true,
    'text' => $transcribedText,
    'model' => $selectedModel
]);
?>
