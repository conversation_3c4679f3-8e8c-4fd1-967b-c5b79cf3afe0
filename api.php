<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Load environment variables
function loadEnv($path) {
    if (!file_exists($path)) {
        return false;
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        
        if (!array_key_exists($name, $_SERVER) && !array_key_exists($name, $_ENV)) {
            putenv(sprintf('%s=%s', $name, $value));
            $_ENV[$name] = $value;
            $_SERVER[$name] = $value;
        }
    }
    return true;
}

// Load .env file
if (!loadEnv('.env')) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Configuration file not found']);
    exit;
}

// Check if required environment variables are set
$apiKey = getenv('GEMINI_API_KEY');
$model = getenv('GEMINI_MODEL') ?: 'gemini-2.5-pro';
$apiUrl = getenv('GEMINI_API_URL') ?: 'https://generativelanguage.googleapis.com/v1beta/models/';
$maxTokens = getenv('MAX_OUTPUT_TOKENS') ?: 64192;
$temperature = getenv('TEMPERATURE') ?: 0;

if (!$apiKey) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'API key not configured']);
    exit;
}

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['image'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'No image data provided']);
    exit;
}

$imageData = $input['image'];
$mimeType = $input['mimeType'] ?? 'image/jpeg';

// Prepare the request to Gemini API
$requestUrl = $apiUrl . $model . ':generateContent?key=' . $apiKey;

$requestBody = [
    'contents' => [
        [
            'parts' => [
                [
                    //'text' => 'Read the handwritten text from this image and transcribe it exactly as it appears, preserving line breaks and structure. If there are any drawings or non-text elements, describe them briefly.'
                    'text' => 'Read this texts and write them out.'
                ],
                [
                    'inline_data' => [
                        'mime_type' => $mimeType,
                        'data' => $imageData
                    ]
                ]
            ]
        ]
    ],
    'generationConfig' => [
        'temperature' => (float)$temperature,
        'maxOutputTokens' => (int)$maxTokens
    ]
];

// Make the API call
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $requestUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestBody));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

if ($curlError) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Network error: ' . $curlError]);
    exit;
}

if ($httpCode !== 200) {
    http_response_code($httpCode);
    $errorData = json_decode($response, true);
    $errorMessage = $errorData['error']['message'] ?? 'Unknown API error';
    echo json_encode(['success' => false, 'error' => 'API Error: ' . $errorMessage]);
    exit;
}

// Parse the response
$responseData = json_decode($response, true);

if (!$responseData || !isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Invalid response from AI service']);
    exit;
}

$transcribedText = $responseData['candidates'][0]['content']['parts'][0]['text'];

// Return success response
echo json_encode([
    'success' => true,
    'text' => $transcribedText,
    'model' => $model
]);
?>
